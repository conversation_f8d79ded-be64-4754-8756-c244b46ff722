<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Steam Tools - Admin Panel</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.min.js"></script>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    :root {
      --background: #0e1419;
      --foreground: #c7d5e0;
      --card: #1b2838;
      --card-foreground: #c7d5e0;
      --border: #2a475e;
      --primary: #66c0f4;
      --primary-foreground: #0e1419;
      --muted: #171a21;
      --muted-foreground: #8b98a5;
      --success: #00d26a;
      --warning: #ffb800;
      --destructive: #cd412b;
      --radius: 10px;
    }
    body {
      font-family: 'Inter', system-ui, -apple-system, Segoe UI, Roboto, sans-serif;
      background: var(--background);
      color: var(--foreground);
      height: 100vh;
      margin: 0;
      overflow: auto; /* allow page scrolling */
    }
    .container {
      max-width: 1100px;
      margin: 0 auto;
      min-height: 100vh; /* allow content to grow */
      display: flex;
      flex-direction: column;
      padding: 28px 40px;
      gap: 28px;
    }
    .card {
      background: linear-gradient(145deg, #1b2838 0%, #16202d 100%);
      border: 1px solid var(--border);
      border-radius: 16px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.35);
    }
    .btn {
      background: var(--primary);
      color: var(--primary-foreground);
      border-radius: 10px;
      min-height: 42px;
      padding: 8px 12px;
      font-weight: 600;
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      line-height: 1.3;
    }
    .btn:hover { filter: brightness(1.1); }
    .btn-secondary{
      background: #4c9eff;
      color: #001018;
    }
    .btn-danger{ background: var(--destructive); color: #fff; }
    .btn-warn{ background: var(--warning); color: #001018; }
    .section-title{
      font-size: 18px; font-weight: 700; color: var(--foreground);
    }
    .muted{ color: var(--muted-foreground); font-size: 13px; }
    .codebox{
      font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
      background: #0b1116;
      border: 1px solid #1f2a37;
      border-radius: 12px;
      padding: 14px;
      /* Fill available space in flex parent; scroll internally */
      min-height: 0;
      height: 100%;
      overflow: auto;
      white-space: pre-wrap;
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- Header (sticky title bar) -->
    <div class="card px-8 py-4 flex items-center justify-between sticky top-0 z-50">
      <div>
        <div class="text-3xl font-bold text-[#66c0f4]">🔧 Admin Panel</div>
        <div class="mt-1 text-sm text-[var(--muted-foreground)]">System management and maintenance tools</div>
      </div>
      <div class="flex gap-3 items-center">
        <button id="lockBtn" class="btn btn-secondary" title="Lock">🔒 Lock</button>
        <button id="minBtn" class="btn" title="Minimize">🗕 Minimize</button>
        <button id="backBtn" class="btn" title="Back">← Back</button>
        <button id="quitBtn" class="btn btn-danger" title="Close App">✖ Exit</button>
      </div>
    </div>

    <div class="grid gap-6" style="grid-template-columns: 3fr 2fr; align-items: stretch;">
      <!-- Left Column: Management -->
      <div class="card p-6 h-full">
        <div class="section-title">⚙️ System Management</div>

        <!-- Steam Path -->
        <div class="mt-4 card p-5">
          <div class="font-semibold text-[var(--foreground)]">🎮 Steam Installation Path</div>
          <div id="steamPathLabel" class="muted mt-2">Loading...</div>
          <div class="mt-4 flex gap-3">
            <button id="browseBtn" class="btn">📁 Browse</button>
            <button id="autoDetectBtn" class="btn btn-secondary">🔍 Auto-detect</button>
          </div>
        </div>

        <!-- Actions -->
        <div class="mt-4 card p-5">
          <div class="font-semibold text-[var(--foreground)]">🎯 Actions</div>
          <div class="grid grid-cols-3 gap-3 mt-3">
            <button id="btnHistory" class="btn w-full">📋 View License History</button>
            <button id="btnLogs" class="btn w-full btn-secondary">📊 View System Logs</button>
            <button id="btnCloseSteam" class="btn w-full btn-warn">🔴 Close Steam</button>
            <button id="btnRemoveHid" class="btn w-full" style="background:#f59e0b;color:#001018">🗑️ Remove hid.dll</button>
            <button id="btnResetStplug" class="btn w-full btn-warn">🔄 Reset stplug-in Folder</button>
            <button id="btnResetAll" class="btn w-full btn-danger">⚠️ Reset All Data</button>
            <button id="btnChangePwd" class="btn w-full">🔐 Change Admin Password</button>
          </div>

          <!-- Developer Options -->
          <div class="mt-6 border-t border-[#1f2a37] pt-4">
            <div class="section-title">🛠 Developer Options</div>
            <div class="muted mt-1">Enable Chromium DevTools for debugging (requires restart).</div>
            <div class="flex items-center gap-3 mt-3">
              <label class="text-sm">DevTools</label>
              <input id="devtoolsToggle" type="checkbox" class="h-4 w-4" />
              <button id="applyRestartBtn" class="btn danger ml-auto">Apply & Restart</button>
            </div>
            <div id="devtoolsStatus" class="muted mt-2"></div>
          </div>
        </div>
      </div>

      <!-- Right Column: Output/Details -->
      <div class="card p-6 flex flex-col h-full">
        <div class="section-title">📦 Output</div>
        <div class="muted mt-1">Results, logs and history will appear here</div>
        <div id="output" class="codebox mt-4 flex-1 min-h-0 overflow-auto"></div>
      </div>
    </div>
  </div>

  <!-- Auth Overlay -->
  <div id="authOverlay" class="fixed inset-0 bg-black/60 backdrop-blur-sm hidden items-center justify-center z-50">
    <div class="card w-[480px] p-6">
      <div class="text-xl font-bold mb-1">Admin Authentication</div>
      <div id="authHelp" class="text-sm text-[var(--muted-foreground)] mb-4">Enter the admin password to access this panel.</div>
      <div>
        <label class="text-sm">Password</label>
        <input id="adminPwd" type="password" class="mt-1 w-full rounded-md bg-[#0b1116] border border-[#1f2a37] px-3 py-2 outline-none focus:ring-2 focus:ring-[#66c0f4]" placeholder="••••••" />
      </div>
      <div id="authError" class="text-red-400 text-sm mt-2 hidden"></div>
      <div class="mt-4 flex gap-3 justify-end">
        <button id="authLoginBtn" class="btn">Unlock</button>
        <button id="authBackBtn" class="btn btn-secondary">Back</button>
      </div>
    </div>
  </div>

  <script>
    function setOutput(obj){
      try {
        if (typeof obj === 'string') {
          document.getElementById('output').textContent = obj;
        } else {
          document.getElementById('output').textContent = JSON.stringify(obj, null, 2);
        }
      } catch(e) {
        console.log(e);
      }
    }

    function appendOutput(text){
      try {
        const output = document.getElementById('output');
        const currentText = output.textContent || '';
        const newText = currentText + (currentText ? '\n' : '') + text;
        output.textContent = newText;
        // Auto-scroll to bottom
        output.scrollTop = output.scrollHeight;
      } catch(e) {
        console.log(e);
      }
    }

    async function refreshSteamPath(){
      try {
        const path = await pywebview.api.get_steam_path();
        document.getElementById('steamPathLabel').textContent = path && path.length ? path : 'Not configured';
      } catch(e){
        document.getElementById('steamPathLabel').textContent = 'Not configured';
      }
    }

    // Auth helpers
    async function showAuthOverlay(){
      const ov = document.getElementById('authOverlay');
      ov.classList.remove('hidden');
      ov.classList.add('flex');
      document.getElementById('adminPwd').value = '';
      const err = document.getElementById('authError');
      err.textContent = '';
      err.classList.add('hidden');
      try{
        const st = await pywebview.api.is_admin_password_set();
        const help = document.getElementById('authHelp');
        if (st && st.success && !st.set){
          help.textContent = 'No admin password set. Please set a new admin password to continue.';
        } else {
          help.textContent = 'Enter the admin password to access this panel.';
        }
      }catch(e){}
      // Focus password field for quick typing and Enter submit
      try { document.getElementById('adminPwd').focus(); } catch(e){}
    }

    function hideAuthOverlay(){
      const ov = document.getElementById('authOverlay');
      ov.classList.add('hidden');
      ov.classList.remove('flex');
    }

    async function ensureAuthenticated(){
      try{
        const s = await pywebview.api.is_admin_session();
        if (!s || !s.authenticated){
          showAuthOverlay();
          return false;
        }
        return true;
      }catch(e){
        showAuthOverlay();
        return false;
      }
    }

    async function doLogin(){
      const pwd = document.getElementById('adminPwd').value || '';
      const status = await pywebview.api.is_admin_password_set();
      if (status && status.success && !status.set){
        if (!pwd){
          const err = document.getElementById('authError');
          err.textContent = 'Please enter a new admin password';
          err.classList.remove('hidden');
          return false;
        }
        const setRes = await pywebview.api.change_admin_password(pwd);
        if (!(setRes && setRes.success)){
          const err = document.getElementById('authError');
          err.textContent = (setRes && setRes.error) ? setRes.error : 'Failed to set password';
          err.classList.remove('hidden');
          return false;
        }
        // After setting, verify to start a session
        const verify = await pywebview.api.verify_admin_password(pwd);
        if (verify && verify.success){
          hideAuthOverlay();
          return true;
        }
        const err = document.getElementById('authError');
        err.textContent = (verify && verify.error) ? verify.error : 'Authentication failed';
        err.classList.remove('hidden');
        return false;
      } else {
        const res = await pywebview.api.verify_admin_password(pwd);
        if (res && res.success){
          hideAuthOverlay();
          return true;
        }
        const err = document.getElementById('authError');
        err.textContent = (res && res.error) ? res.error : 'Authentication failed';
        err.classList.remove('hidden');
        return false;
      }
    }

    // Wire buttons
    async function wire(){
      document.getElementById('backBtn').addEventListener('click', async ()=>{
        try {
          if (window.pywebview && window.pywebview.api && typeof window.pywebview.api.log_event === 'function') {
            await window.pywebview.api.log_event('Admin panel action: Back button clicked; navigating to License page');
          }
        } catch (_) {}
        window.location.href = 'steam_license_webview.html';
      });
      document.getElementById('quitBtn').addEventListener('click', async ()=>{
        try { await pywebview.api.close_app(); } catch(e){}
      });
      document.getElementById('minBtn').addEventListener('click', async ()=>{
        try { await pywebview.api.minimize_window(); } catch(e){}
      });
      document.getElementById('lockBtn').addEventListener('click', async ()=>{
        try { await pywebview.api.logout_admin_session(); } catch(e){}
        showAuthOverlay();
      });
      document.getElementById('authLoginBtn').addEventListener('click', async ()=>{
        const ok = await doLogin();
        if (ok){ await refreshSteamPath(); }
      });
      document.getElementById('authBackBtn').addEventListener('click', async ()=>{
        try {
          if (window.pywebview && window.pywebview.api && typeof window.pywebview.api.log_event === 'function') {
            await window.pywebview.api.log_event('Admin panel action: Auth Back clicked; navigating to License page');
          }
        } catch (_) {}
        window.location.href = 'steam_license_webview.html';
      });

      // Enter key on password input = Unlock
      const pwdInput = document.getElementById('adminPwd');
      pwdInput.addEventListener('keydown', async (e)=>{
        if ((e.key || '').toLowerCase() === 'enter'){
          e.preventDefault();
          const ok = await doLogin();
          if (ok){ await refreshSteamPath(); }
        }
      });

      document.getElementById('browseBtn').addEventListener('click', async ()=>{
        if (!await ensureAuthenticated()) return;
        const res = await pywebview.api.browse_steam_path();
        await refreshSteamPath();
        setOutput(res);
      });
      document.getElementById('autoDetectBtn').addEventListener('click', async ()=>{
        if (!await ensureAuthenticated()) return;
        const res = await pywebview.api.auto_detect_steam_path();
        await refreshSteamPath();
        setOutput(res);
      });

      document.getElementById('btnHistory').addEventListener('click', async ()=>{
        if (!await ensureAuthenticated()) return;
        const res = await pywebview.api.view_license_history();
        setOutput(res);
      });
      document.getElementById('btnLogs').addEventListener('click', async ()=>{
        if (!await ensureAuthenticated()) return;
        const res = await pywebview.api.view_system_logs(300);
        if (res && res.logs) {
          setOutput(res.logs.join('\n'));
        } else {
          setOutput(res);
        }
      });
      document.getElementById('btnCloseSteam').addEventListener('click', async ()=>{
        if (!await ensureAuthenticated()) return;
        if (!confirm('Close all Steam processes?')) return;
        const res = await pywebview.api.close_steam_processes();
        setOutput(res);
      });
      document.getElementById('btnRemoveHid').addEventListener('click', async ()=>{
        if (!await ensureAuthenticated()) return;
        const res = await pywebview.api.remove_hid_dll();
        setOutput(res);
      });
      document.getElementById('btnResetStplug').addEventListener('click', async ()=>{
        if (!await ensureAuthenticated()) return;
        if (!confirm('Remove the stplug-in folder?')) return;
        const res = await pywebview.api.reset_stplug_folder();
        setOutput(res);
      });
      document.getElementById('btnResetAll').addEventListener('click', async ()=>{
        if (!await ensureAuthenticated()) return;
        if (!confirm('Reset ALL app data to defaults? This cannot be undone.')) return;
        const res = await pywebview.api.reset_all_data();
        setOutput(res);
      });
      document.getElementById('btnChangePwd').addEventListener('click', async ()=>{
        if (!await ensureAuthenticated()) return;
        const pwd = prompt('Enter new admin password:');
        if (pwd && pwd.trim().length){
          const res = await pywebview.api.change_admin_password(pwd.trim());
          setOutput(res);
        }
      });

      // Developer Options: load status, toggle, and restart
      async function loadDevtoolsStatus(){
        if (!await ensureAuthenticated()) return;
        try{
          const st = await pywebview.api.get_debug_enabled();
          const enabled = !!(st && st.success && st.enabled);
          const toggle = document.getElementById('devtoolsToggle');
          const label = document.getElementById('devtoolsStatus');
          toggle.checked = enabled;
          label.textContent = enabled ? 'DevTools is ENABLED. Changes require restart.' : 'DevTools is DISABLED.';
        }catch(e){
          appendOutput('Failed to read DevTools status: ' + e);
        }
      }

      document.getElementById('devtoolsToggle').addEventListener('change', async (e)=>{
        if (!await ensureAuthenticated()) { e.preventDefault(); return; }
        const wanted = !!e.target.checked;
        try {
            const res = await pywebview.api.set_debug_enabled(wanted);
            if (res && res.success){
              appendOutput(`DevTools setting updated. Click "Apply & Restart" to take effect.`);
            } else {
              appendOutput('Failed to update DevTools: ' + (res && res.error ? res.error : 'Unknown error'));
            }
        } catch(err) {
            appendOutput('Error calling set_debug_enabled: ' + err);
        } finally {
            // Always reload status from backend to ensure sync
            await loadDevtoolsStatus();
        }
      });

      document.getElementById('applyRestartBtn').addEventListener('click', async ()=>{
        if (!await ensureAuthenticated()) return;
        appendOutput('Restarting app to apply settings...');
        try {
          if (window.pywebview && window.pywebview.api && typeof window.pywebview.api.log_event === 'function') {
            await window.pywebview.api.log_event('Admin panel action: Apply & Restart clicked; invoking restart_app');
          }
        } catch (_) {}
        await pywebview.api.restart_app();
      });

      // Initial load of DevTools status after auth
      (async ()=>{
        const ok = await ensureAuthenticated();
        if (ok) await loadDevtoolsStatus();
      })();

      // Ctrl+Shift+A toggles back to license page
      document.addEventListener('keydown', async (e)=>{
        const key = e.key || '';
        if (e.ctrlKey && e.shiftKey && key.toUpperCase() === 'A') {
          e.preventDefault();
          try {
            if (window.pywebview && window.pywebview.api && typeof window.pywebview.api.log_event === 'function') {
              await window.pywebview.api.log_event('Shortcut pressed: Ctrl+Shift+A on Admin page; navigating to License page');
            }
          } catch (_) {}
          window.location.href = 'steam_license_webview.html';
        }
      });
    }

    // Initialize
    (async function init(){
      await wire();
      const authed = await ensureAuthenticated();
      if (authed){ await refreshSteamPath(); }
      // Periodically re-check session
      setInterval(async ()=>{
        const s = await pywebview.api.is_admin_session();
        if (!s || !s.authenticated) showAuthOverlay();
      }, 60000);
    })();
  </script>
</body>
</html>
